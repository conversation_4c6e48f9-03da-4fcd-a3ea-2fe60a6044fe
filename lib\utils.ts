import AsyncStorage from "@react-native-async-storage/async-storage";
import { type ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export type UserRole = "student" | "teacher" | "HOD" | "super_admin";

// Database profile type matching the new schema
export interface Profile {
  id: string;
  updated_at: string | null;
  full_name: string | null;
  avatar_url: string | null;
  email: string | null;
  gender: string | null;
  phone: string | null;
  role: UserRole[] | null;
  onboarding_completed: boolean | null;
  isverify: boolean | null;
}

// User role management - Updated to use database
export async function getUserRole(): Promise<UserRole | null> {
  try {
    const { supabase } = await import("./supabase");
    const {
      data: { user },
    } = await supabase.auth.getUser();

    if (!user) return null;

    const profile = await getProfile(user.id);
    return profile?.role?.[0] || null;
  } catch (error) {
    console.error("Error getting user role:", error);
    // Fallback to AsyncStorage for backward compatibility
    try {
      const role = await AsyncStorage.getItem("userRole");
      return role as UserRole | null;
    } catch (fallbackError) {
      console.error(
        "Error getting user role from AsyncStorage:",
        fallbackError
      );
      return null;
    }
  }
}

export async function setUserRole(role: UserRole): Promise<void> {
  try {
    // Keep AsyncStorage for backward compatibility during transition
    await AsyncStorage.setItem("userRole", role);
  } catch (error) {
    console.error("Error setting user role:", error);
  }
}

export async function clearUserRole(): Promise<void> {
  try {
    await AsyncStorage.removeItem("userRole");
  } catch (error) {
    console.error("Error clearing user role:", error);
  }
}

// New database-based role functions
export async function getUserRoles(): Promise<UserRole[]> {
  try {
    const { supabase } = await import("./supabase");
    const {
      data: { user },
    } = await supabase.auth.getUser();

    if (!user) return [];

    const profile = await getProfile(user.id);
    return profile?.role || [];
  } catch (error) {
    console.error("Error getting user roles:", error);
    return [];
  }
}

export async function hasRole(role: UserRole): Promise<boolean> {
  try {
    const roles = await getUserRoles();
    return roles.includes(role);
  } catch (error) {
    console.error("Error checking user role:", error);
    return false;
  }
}

export async function isSuperAdmin(): Promise<boolean> {
  return await hasRole("super_admin");
}

// Database profile management functions
export async function createProfile(
  userId: string,
  email: string,
  role: UserRole[]
): Promise<{ success: boolean; error?: string }> {
  try {
    const { supabase } = await import("./supabase");

    const { error } = await supabase.from("profiles").insert({
      id: userId,
      email: email,
      role: role,
      onboarding_completed: false,
      isverify: false,
      updated_at: new Date().toISOString(),
    });

    if (error) {
      console.error("Error creating profile:", error);
      return { success: false, error: error.message };
    }

    return { success: true };
  } catch (error) {
    console.error("Error creating profile:", error);
    return { success: false, error: "An unexpected error occurred" };
  }
}

export async function getProfile(userId: string): Promise<Profile | null> {
  try {
    const { supabase } = await import("./supabase");

    const { data, error } = await supabase
      .from("profiles")
      .select("*")
      .eq("id", userId)
      .single();

    if (error) {
      console.error("Error fetching profile:", error);
      return null;
    }

    return data as Profile;
  } catch (error) {
    console.error("Error fetching profile:", error);
    return null;
  }
}

export async function updateProfile(
  userId: string,
  updates: Partial<Profile>
): Promise<{ success: boolean; error?: string }> {
  try {
    const { supabase } = await import("./supabase");

    const { error } = await supabase
      .from("profiles")
      .update({
        ...updates,
        updated_at: new Date().toISOString(),
      })
      .eq("id", userId);

    if (error) {
      console.error("Error updating profile:", error);
      return { success: false, error: error.message };
    }

    return { success: true };
  } catch (error) {
    console.error("Error updating profile:", error);
    return { success: false, error: "An unexpected error occurred" };
  }
}

// Super admin functions
export async function getPendingVerifications(): Promise<Profile[]> {
  try {
    const { supabase } = await import("./supabase");

    const { data, error } = await supabase
      .from("profiles")
      .select("*")
      .eq("onboarding_completed", true)
      .eq("isverify", false)
      .order("updated_at", { ascending: false });

    if (error) {
      console.error("Error fetching pending verifications:", error);
      return [];
    }

    return data as Profile[];
  } catch (error) {
    console.error("Error fetching pending verifications:", error);
    return [];
  }
}

export async function approveUser(
  userId: string
): Promise<{ success: boolean; error?: string }> {
  try {
    const { supabase } = await import("./supabase");

    const { error } = await supabase
      .from("profiles")
      .update({
        isverify: true,
        updated_at: new Date().toISOString(),
      })
      .eq("id", userId);

    if (error) {
      console.error("Error approving user:", error);
      return { success: false, error: error.message };
    }

    return { success: true };
  } catch (error) {
    console.error("Error approving user:", error);
    return { success: false, error: "An unexpected error occurred" };
  }
}

export async function rejectUser(
  userId: string
): Promise<{ success: boolean; error?: string }> {
  try {
    const { supabase } = await import("./supabase");

    // Delete the profile and auth user
    const { error: profileError } = await supabase
      .from("profiles")
      .delete()
      .eq("id", userId);

    if (profileError) {
      console.error("Error rejecting user:", profileError);
      return { success: false, error: profileError.message };
    }

    return { success: true };
  } catch (error) {
    console.error("Error rejecting user:", error);
    return { success: false, error: "An unexpected error occurred" };
  }
}
