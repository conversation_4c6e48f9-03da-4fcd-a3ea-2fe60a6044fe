import { Session } from "@supabase/supabase-js";
import { useRouter } from "expo-router";
import { useEffect, useState } from "react";
import { ActivityIndicator, Text, View } from "react-native";
import { supabase } from "~/lib/supabase";
import { useColorScheme } from "~/lib/useColorScheme";
import { getProfile, type Profile } from "~/lib/utils";

export default function App() {
  const [session, setSession] = useState<Session | null>(null);
  const [profile, setProfile] = useState<Profile | null>(null);
  const [loading, setLoading] = useState(true);
  const router = useRouter();
  const { isDarkColorScheme } = useColorScheme();

  useEffect(() => {
    const initializeAuth = async () => {
      try {
        const {
          data: { session },
        } = await supabase.auth.getSession();
        setSession(session);

        if (session?.user) {
          const userProfile = await getProfile(session.user.id);
          setProfile(userProfile);
        }
      } catch (error) {
        console.error("Error initializing auth:", error);
      } finally {
        setLoading(false);
      }
    };

    initializeAuth();

    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange(async (_event, session) => {
      setSession(session);

      if (session?.user) {
        const userProfile = await getProfile(session.user.id);
        setProfile(userProfile);
      } else {
        setProfile(null);
      }
      setLoading(false);
    });

    return () => subscription.unsubscribe();
  }, []);

  // Show loading screen while checking auth
  if (loading) {
    return (
      <View
        className={`flex-1 items-center justify-center ${
          isDarkColorScheme ? "bg-gray-900" : "bg-white"
        }`}
      >
        <ActivityIndicator
          size="large"
          color={isDarkColorScheme ? "#3B82F6" : "#2563EB"}
        />
        <Text
          className={`mt-4 text-lg ${
            isDarkColorScheme ? "text-white" : "text-gray-900"
          }`}
        >
          Loading...
        </Text>
      </View>
    );
  }

  // If no session, redirect to auth welcome screen
  if (!session || !session.user) {
    router.replace("/(auth)/welcome");
    return null;
  }

  // Check profile status and redirect accordingly
  if (profile) {
    // If profile exists but onboarding not completed, redirect to profile completion
    if (!profile.onboarding_completed) {
      router.replace("/(auth)/profile-completion");
      return null;
    }

    // If onboarding completed but not verified (for HODs/teachers), redirect to pending approval
    if (
      (profile.onboarding_completed &&
        !profile.isverify &&
        profile.role?.includes("HOD" as any)) ||
      profile.role?.includes("teacher" as any)
    ) {
      router.replace("/(auth)/pending-approval");
      return null;
    }
  }

  // If authenticated, redirect to tabs
  router.replace("/(tabs)");

  return (
    <View
      className={`flex-1 items-center justify-center ${
        isDarkColorScheme ? "bg-gray-900" : "bg-white"
      }`}
    >
      <Text
        className={`text-lg ${
          isDarkColorScheme ? "text-white" : "text-gray-900"
        }`}
      >
        Redirecting...
      </Text>
    </View>
  );
}
